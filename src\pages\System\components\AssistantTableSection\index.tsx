import React, { useState, useEffect } from 'react';
import { Button, Input, Select, Space, Table, Pagination, Popconfirm, message, Modal, Spin, Tooltip } from 'antd';
import moment from 'moment';
import './index.less';
import AssistantModal from './components/AssistantModal';
import KnowledgeBaseModal from './components/KnowledgeBaseModal';
import { addAiRole, deleteAiRole, updateAiRole, getAiRoleById, listAiRoleVoByPage } from '@/services/DataLoom/assistantController';

const { Option } = Select;

// 定义助手数据类型 - 使用API返回的AiRole类型
type AssistantData = API.AiRole;

// 定义查询参数类型
interface QueryParams {
  assistantName?: string;
  functionDes?: string;
  type?: string;
  current?: number;
  pageSize?: number;
}

const AssistantTableSection: React.FC = () => {
  // 状态管理
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [knowledgeBaseModalVisible, setKnowledgeBaseModalVisible] = useState(false);
  const [editingAssistant, setEditingAssistant] = useState<AssistantData | null>(null);
  const [currentAssistant, setCurrentAssistant] = useState<AssistantData | null>(null);
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<AssistantData[]>([]);
  const [total, setTotal] = useState(0);
  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // 查询参数
  const [queryParams, setQueryParams] = useState<QueryParams>({
    current: 1,
    pageSize: 10,
  });

  // 获取助手列表
  const fetchAssistantList = async (params: QueryParams = queryParams) => {
    try {
      setLoading(true);
      const response = await listAiRoleVoByPage({
        ...params,
        current: params.current || 1,
        pageSize: params.pageSize || 10,
      });

      if (response.code === 0 && response.data) {
        const newData = response.data.records || [];
        setData(newData);
        setTotal(response.data.total || 0);
        return newData; // 返回最新的数据
      } else {
        message.error(response.message || '获取助手列表失败');
        return [];
      }
    } catch (error) {
      console.error('获取助手列表失败:', error);
      message.error('获取助手列表失败');
      return [];
    } finally {
      setLoading(false);
    }
  };

  // 删除助手
  const handleDelete = async (record: AssistantData) => {
    try {
      const response = await deleteAiRole({ id: record.id });
      if (response.code === 0) {
        message.success('删除成功');
        fetchAssistantList(); // 重新获取列表
      } else {
        message.error(response.message || '删除失败');
      }
    } catch (error) {
      console.error('删除助手失败:', error);
      message.error('删除失败');
    }
  };

  // 知识库管理
  const handleKnowledgeBase = (record: AssistantData) => {
    setCurrentAssistant(record);
    setKnowledgeBaseModalVisible(true);
  };

  // 查询
  const handleSearch = () => {
    const newParams = { ...queryParams, current: 1 };
    setQueryParams(newParams);
    fetchAssistantList(newParams);
  };

  // 重置
  const handleReset = () => {
    const resetParams = { current: 1, pageSize: 10 };
    setQueryParams(resetParams);
    fetchAssistantList(resetParams);
  };

  // 分页变化
  const handlePageChange = (page: number, size?: number) => {
    const newParams = {
      ...queryParams,
      current: page,
      pageSize: size || pageSize,
    };
    setQueryParams(newParams);
    setCurrent(page);
    setPageSize(size || pageSize);
    fetchAssistantList(newParams);
  };

  // 初始化加载
  useEffect(() => {
    fetchAssistantList();
  }, []);

  const columns = [
    {
      title: '助手名称',
      dataIndex: 'assistantName',
    },
    {
      title: '功能描述',
      dataIndex: 'functionDes',
      ellipsis: true,
      render: (text: string) => (
        <Tooltip placement="topLeft" title={text}>
          <span style={{ whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis', display: 'inline-block', maxWidth: 200 }}>
            {text}
          </span>
        </Tooltip>
      ),
    },
    {
      title: '助手类型',
      dataIndex: 'type',
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      render: (text: string) => (text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : '-'),
    },
    {
      title: '操作',
      dataIndex: 'action',
      render: (_: any, record: AssistantData) => (
        <Space size={10} className="system-action">
          <a
            className="system-action-edit"
            onClick={() => {
              setEditingAssistant(record);
              setEditModalVisible(true);
            }}
          >
            编辑
          </a>
          <a className="system-action-knowledge" onClick={() => handleKnowledgeBase(record)}>
            知识库
          </a>
          <a
            className="system-action-delete"
            onClick={() => {
              Modal.confirm({
                title: '确认删除该助手吗？',
                content: '此操作不可恢复，是否继续？',
                okText: '确认',
                cancelText: '取消',
                onOk: () => handleDelete(record),
              });
            }}
          >
            删除
          </a>
        </Space>
      ),
    },
  ];

  return (
    <div className="assistant-table-section">
      {/* 筛选区 */}
      <div className="system-filter">
        <Space size={16} align="center" className="system-filter-row">
          <div className="system-filter-item">
            <span className="system-filter-item-label">助手名称：</span>
            <Input
              placeholder="请输入助手名称"
              value={queryParams.assistantName}
              onChange={(e) => setQueryParams((prev) => ({ ...prev, assistantName: e.target.value }))}
            />
          </div>
          <div className="system-filter-item">
            <span className="system-filter-item-label">功能描述：</span>
            <Input
              placeholder="请输入功能描述"
              value={queryParams.functionDes}
              onChange={(e) => setQueryParams((prev) => ({ ...prev, functionDes: e.target.value }))}
            />
          </div>
          <div className="system-filter-item">
            <span className="system-filter-item-label">助手类型：</span>
            <Input
              value={queryParams.type}
              onChange={(e) => setQueryParams((prev) => ({ ...prev, type: e.target.value }))}
              placeholder="请选择助手类型"
              allowClear
            />
          </div>
          <div className="system-filter-btns">
            <Button className="system-filter-reset" onClick={handleReset}>
              重置
            </Button>
            <Button type="primary" className="system-filter-search" onClick={handleSearch}>
              查询
            </Button>
          </div>
        </Space>
      </div>

      {/* 助手表格 */}
      <div className="system-table-wrapper">
        {/* 操作按钮 */}
        <div className="system-operations">
          <Space>
            <Button type="primary" className="system-operations-create" onClick={() => setAddModalVisible(true)}>
              新建助手
            </Button>
          </Space>
        </div>

        <Spin spinning={loading}>
          <Table
            columns={columns}
            dataSource={data}
            pagination={false}
            bordered={false}
            scroll={{ x: 1200, y: 'calc(100vh - 430px)' }}
            rowKey="id"
          />
        </Spin>

        {/* 分页 */}
        <div className="system-pagination-bar">
          <Pagination
            current={current}
            total={total}
            pageSize={pageSize}
            showSizeChanger={true}
            showQuickJumper={true}
            showTotal={(total) => `共 ${total} 条`}
            onChange={handlePageChange}
            onShowSizeChange={handlePageChange}
          />
        </div>
      </div>

      {/* 新建助手弹框 */}
      <AssistantModal
        visible={addModalVisible}
        mode="add"
        onCancel={() => setAddModalVisible(false)}
        onOk={() => {
          setAddModalVisible(false);
          fetchAssistantList();
        }}
      />
      {/* 编辑助手弹框 */}
      <AssistantModal
        visible={editModalVisible}
        mode="edit"
        initialValues={editingAssistant}
        onCancel={() => {
          setEditModalVisible(false);
          setEditingAssistant(null);
        }}
        onOk={() => {
          setEditModalVisible(false);
          setEditingAssistant(null);
          fetchAssistantList();
        }}
      />
      {/* 知识库弹框 */}
      <KnowledgeBaseModal
        visible={knowledgeBaseModalVisible}
        assistantData={currentAssistant}
        onCancel={() => {
          setKnowledgeBaseModalVisible(false);
          setCurrentAssistant(null);
        }}
        onRefresh={async () => {
          const newData = await fetchAssistantList(); // 刷新助手列表
          // 刷新后更新currentAssistant数据
          if (currentAssistant && currentAssistant.id) {
            const updatedAssistant = newData.find((item) => item.id === currentAssistant.id);
            if (updatedAssistant) {
              setCurrentAssistant(updatedAssistant);
            }
          }
        }}
      />
    </div>
  );
};

export default AssistantTableSection;
