.knowledge-base-modal {
  .knowledge-base-content {
    .knowledge-base-header {
      padding: 0 24px;
      margin-bottom: 24px;

      .file-count {
        font-size: 16px;
        font-weight: 500;
        color: #262626;
      }
    }

    .actions-bar {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      padding: 0 24px;
      gap: 10px;

      .left-section {
        display: flex;
        align-items: center;
        gap: 10px;
      }

      .search-input {
        width: 380px;

        .ant-input-prefix {
          color: #bfbfbf;
        }
      }

      .action-buttons {
        display: flex;
        gap: 10px;
      }
    }

    .file-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
      gap: 16px;
      max-height: 500px;
      overflow-y: auto;
      padding: 12px 24px;

      .file-card {
        background: #ECF2FF;
        border: 2px solid transparent;
        border-radius: 8px;
        padding: 16px 24px;
        transition: all 0.3s ease;
        cursor: pointer;
        position: relative;

        &:hover {
          border: 2px solid #f2f9ff;
          box-shadow: 0 0 10px 4px rgba(0, 67, 213, 0.1);
          transform: translateY(-2px);
        }

        .file-checkbox {
          position: absolute;
          top: 8px;
          left: 24px;
          z-index: 2;
          opacity: 0;
          transition: opacity 0.2s ease;

          &.checked {
            opacity: 1;
          }
        }

        &:hover .file-checkbox {
          opacity: 1;
        }

        // 上传中状态
        &.uploading {
          opacity: 0.7;
          pointer-events: none;

          .file-card-content {
            position: relative;

            &::after {
              content: '上传中...';
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              background: rgba(0, 0, 0, 0.8);
              color: white;
              padding: 4px 8px;
              border-radius: 4px;
              font-size: 12px;
              z-index: 10;
            }
          }
        }

        .file-card-content {
          text-align: center;

          .file-icon-wrapper {
            position: relative;
            margin-bottom: 12px;

            .file-icon {
              width: 60px;
              height: 60px;
              margin: 0 auto;
              display: block;
              object-fit: contain;
            }

            .file-actions {
              position: absolute;
              top: -10px;
              right: 0;
              display: flex;
              align-items: center;
              justify-content: center;
              cursor: pointer;
            }
          }

          .file-info {
            margin-bottom: 12px;

            .file-name {
              font-size: 14px;
              color: #262626;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              line-height: 1.4;
            }
          }

          .file-card-actions {
            display: flex;
            justify-content: space-between;
            gap: 8px;
            padding-top: 12px;
            margin-top: 12px;

            .action-btn {
              padding: 4px 12px;
              font-size: 12px;
              height: 32px;
              border-radius: 4px;
              border: none;

              &.preview-btn {
                color: #2868e7;
                background: #d3e4ff;

                &:hover {
                  color: #ffffff;
                  background: #2868e7;
                  transform: translateY(-1px);
                  box-shadow: 0 2px 8px rgba(40, 104, 231, 0.3);
                }
              }

              &.download-btn {
                color: #2868e7;
                background: #d3e4ff;

                &:hover {
                  color: #ffffff;
                  background: #2868e7;
                  transform: translateY(-1px);
                  box-shadow: 0 2px 8px rgba(40, 104, 231, 0.3);
                }
              }

              .anticon {
                font-size: 12px;
              }
            }
          }
        }
      }
    }

    .empty-state {
      text-align: center;
      padding: 60px 0;
      color: #8c8c8c;

      .empty-icon {
        font-size: 48px;
        margin-bottom: 16px;
      }

      .empty-text {
        font-size: 14px;
      }
    }
  }
}

// 滚动条样式
.file-grid::-webkit-scrollbar {
  width: 6px;
}

.file-grid::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.file-grid::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;

  &:hover {
    background: #a8a8a8;
  }
}

// Popover 弹出层样式
.file-action-menu {
  padding: 4px 0;
  .ant-popover-inner {
    padding: 0;
  }

  .action-item {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    border-radius: 4px;
    transition: background-color 0.2s;
    font-size: 14px;
    min-width: 80px;

    &.delete-item {
      color: #262628;
      &:hover {
        color: #2868e7;
      }
    }

    span {
      user-select: none;
    }
  }
}

// 文件操作图标样式
.file-action-icon {
  width: 24px;
  height: 24px;
  cursor: pointer;
  opacity: 0.6;
  transition: opacity 0.2s;

  &:hover {
    opacity: 1;
  }
}
